#!/usr/bin/env python3
"""
Financial Safety Coordinator
Provides financial safety coordination for MEV operations
"""

import logging
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class FinancialSafetyCoordinator:
    """Coordinates financial safety across MEV operations"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.safety_limits = {
            'max_position_size': 10.0,
            'max_daily_loss': 1.0,
            'min_profit_threshold': 0.001,
            'max_slippage': 0.01
        }
        self.active_positions = []
        self.daily_pnl = 0.0
    
    async def validate_position_safety(self, position_data: Dict[str, Any]) -> bool:
        """Validate position safety limits"""
        position_size = position_data.get('size', 0)
        
        if position_size > self.safety_limits['max_position_size']:
            logger.warning(f"Position size {position_size} exceeds limit {self.safety_limits['max_position_size']}")
            return False
        
        return True
    
    async def check_daily_limits(self) -> bool:
        """Check daily trading limits"""
        if abs(self.daily_pnl) > self.safety_limits['max_daily_loss']:
            logger.warning(f"Daily loss {abs(self.daily_pnl)} exceeds limit {self.safety_limits['max_daily_loss']}")
            return False
        
        return True
    
    async def validate_trade_safety(self, trade_data: Dict[str, Any]) -> bool:
        """Validate trade safety"""
        expected_profit = trade_data.get('expected_profit', 0)
        slippage = trade_data.get('slippage', 0)
        
        if expected_profit < self.safety_limits['min_profit_threshold']:
            logger.info(f"Trade profit {expected_profit} below threshold {self.safety_limits['min_profit_threshold']}")
            return False
        
        if slippage > self.safety_limits['max_slippage']:
            logger.warning(f"Trade slippage {slippage} exceeds limit {self.safety_limits['max_slippage']}")
            return False
        
        return True
    
    def record_trade_result(self, trade_result: Dict[str, Any]):
        """Record trade result for safety tracking"""
        pnl = trade_result.get('pnl', 0)
        self.daily_pnl += pnl
        logger.info(f"Trade recorded: PnL={pnl}, Daily PnL={self.daily_pnl}")
    
    def get_safety_status(self) -> Dict[str, Any]:
        """Get current safety status"""
        return {
            'daily_pnl': self.daily_pnl,
            'active_positions': len(self.active_positions),
            'limits': self.safety_limits,
            'status': 'safe' if self.daily_pnl > -self.safety_limits['max_daily_loss'] else 'at_risk'
        }
