#!/usr/bin/env python3
"""
🚀 Enhanced OpportunityScanner Component

Extracted from MEV Engine monolith following proven delegation pattern.
Contains all unified opportunity scanning, enhancement, and intelligence logic.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List

logger = logging.getLogger(__name__)


class EnhancedOpportunityScanner:
    """
    Enhanced opportunity scanning with unified intelligence, enhancement, and optimization.

    Extracted from StandaloneMevEngine monolith following proven delegation pattern.
    """

    def __init__(self, mev_engine_ref, config_manager, arbitrage_engine=None, sandwich_detector=None):
        """Initialize Enhanced OpportunityScanner with MEV engine reference"""
        self.mev_engine_ref = mev_engine_ref
        self.config_manager = config_manager
        self.arbitrage_engine = arbitrage_engine
        self.sandwich_detector = sandwich_detector

        # Initialize enhancement metrics
        self.enhancement_metrics = {
            "total_enhancement_cycles": 0,
            "average_token_expansion_factor": 0.0,
            "intelligence_signals_generated": 0,
            "enhanced_opportunities_identified": 0,
            "average_enhancement_latency_ms": 0.0,
        }

        logger.info("🚀 Enhanced OpportunityScanner initialized with proven delegation pattern")

    async def scan_unified_opportunities(self) -> List:
        """Main unified opportunity scanning method - now integrated with Jupiter micro-arbitrage and MultiAggregatorRouter"""

        all_opportunities = []

        # 🚀 JUPITER MICRO-ARBITRAGE INTEGRATION: Use existing OpportunityScanner with complete functionality
        if hasattr(self.mev_engine_ref, "opportunity_scanner") and self.mev_engine_ref.opportunity_scanner:
            # Delegate to existing scanner that already includes Jupiter micro-arbitrage scanning
            opportunities = await self.mev_engine_ref.opportunity_scanner.scan_all_opportunities()
            all_opportunities.extend(opportunities)
            logger.info(f"🔍 Enhanced OpportunityScanner: Found {len(opportunities)} base opportunities")

        # 🌐 NEW: MultiAggregatorRouter Integration for cross-DEX opportunities
        cross_dex_opportunities = []
        try:
            from dex_providers.shared.aggregator_health_monitor import AggregatorHealthMonitor
            from dex_providers.shared.multi_aggregator_router import MultiAggregatorRouter

            # Initialize router with health monitoring
            health_monitor = AggregatorHealthMonitor()
            await health_monitor.start()

            router = MultiAggregatorRouter(
                health_monitor=health_monitor,
                exclude_providers=["jupiter"] if hasattr(self.config_manager, "jupiter_bypass_mode") else [],
            )
            await router.start()

            # Get cross-DEX opportunities for major token pairs (F001 FIX: Dynamic market data)
            from config.enhanced_config_manager import EnhancedConfigManager
            from dex_providers.shared.market_data_service import MarketDataService

            enhanced_config = EnhancedConfigManager()
            market_data_service = MarketDataService(enhanced_config)
            major_pairs = await market_data_service.get_active_trading_pairs(
                min_volume_24h=enhanced_config.get_min_volume_24h_usd(),
                min_liquidity=enhanced_config.get_min_liquidity_usd(),
                max_pairs=enhanced_config.get_max_cross_dex_pairs(),
            )

            for input_mint, output_mint in major_pairs:
                # F002 FIX: Dynamic position sizing based on market conditions and risk
                from dex_providers.shared.position_sizing_manager import PositionSizingManager

                position_manager = PositionSizingManager(enhanced_config)
                market_conditions = await market_data_service.get_current_market_conditions()

                amount_sol = position_manager.get_cross_dex_amount(
                    strategy_type="cross_dex_arbitrage",
                    market_volatility=market_conditions.volatility,
                    available_balance=None,  # Will use wallet manager if available
                )
                amount = int(amount_sol * enhanced_config.get_sol_lamports_multiplier())  # Convert to lamports
                best_route = await router.get_best_quote(input_mint, output_mint, amount)

                if best_route:
                    provider_name, quote = best_route
                    # Convert to MevOpportunity format for compatibility
                    cross_dex_opp = self._create_mev_opportunity_from_route(
                        provider_name, quote, input_mint, output_mint, amount
                    )
                    if cross_dex_opp:
                        cross_dex_opportunities.append(cross_dex_opp)

            logger.info(f"✅ MultiAggregatorRouter: Found {len(cross_dex_opportunities)} cross-DEX opportunities")

        except Exception as e:
            logger.warning(f"⚠️ MultiAggregatorRouter integration error: {e}")

        # Combine all opportunities
        all_opportunities.extend(cross_dex_opportunities)

        # 📊 NEW: Real-Time Intelligence Enhancement
        try:
            if hasattr(self.mev_engine_ref, "intelligence_system") and self.mev_engine_ref.intelligence_system:
                intelligence_system = self.mev_engine_ref.intelligence_system

                # Get real-time volume intelligence for opportunity enhancement
                enhanced_opportunities = []
                for opp in all_opportunities:
                    try:
                        # Extract token information from opportunity
                        token_mint = None
                        token_symbol = None

                        if hasattr(opp, "execution_params") and opp.execution_params:
                            token_mint = opp.execution_params.get("input_mint") or opp.execution_params.get(
                                "token_mint"
                            )
                            if not token_mint and "token_path" in opp.execution_params:
                                token_path = opp.execution_params.get("token_path", [])
                                if token_path and len(token_path) > 0:
                                    token_symbol = token_path[0]

                        # Get real-time intelligence if we have token info
                        if token_mint or token_symbol:
                            # Use volume collector for real-time market data
                            volume_collector = getattr(intelligence_system, "volume_monitor", None)
                            if volume_collector and hasattr(volume_collector, "volume_collector"):
                                volume_data = await volume_collector.volume_collector.collect_volume_data(
                                    token_mint or "", token_symbol or "UNKNOWN"
                                )

                                # F003 FIX: Configurable liquidity threshold
                                min_liquidity_threshold = config_manager.get_min_liquidity_threshold()
                                if volume_data and volume_data.volume_usd > min_liquidity_threshold:
                                    # F005 FIX: Configurable confidence multiplier
                                    confidence_boost = config_manager.get_confidence_multiplier()
                                    max_confidence = config_manager.get_max_confidence_score()
                                    opp.confidence_score = min(opp.confidence_score * confidence_boost, max_confidence)
                                    if hasattr(opp, "execution_params"):
                                        opp.execution_params["market_intelligence"] = {
                                            "volume_usd": volume_data.volume_usd,
                                            "liquidity_score": getattr(volume_data, "liquidity_score", 0.5),
                                            "intelligence_enhanced": True,
                                        }
                                    enhanced_opportunities.append(opp)
                                else:
                                    enhanced_opportunities.append(opp)  # Keep original if no enhancement
                            else:
                                enhanced_opportunities.append(opp)  # Keep original if no collector
                        else:
                            enhanced_opportunities.append(opp)  # Keep original if no token info

                    except Exception as enhance_error:
                        logger.debug(f"Intelligence enhancement failed for opportunity: {enhance_error}")
                        enhanced_opportunities.append(opp)  # Keep original on error

                all_opportunities = enhanced_opportunities
                logger.info(f"📊 Intelligence enhancement applied to {len(all_opportunities)} opportunities")

        except Exception as e:
            logger.warning(f"⚠️ Real-time intelligence integration error: {e}")

        # Log breakdown by type for visibility
        type_counts = {}
        for opp in all_opportunities:
            opp_type = getattr(opp, "opportunity_type", "unknown")
            type_counts[opp_type] = type_counts.get(opp_type, 0) + 1

        if type_counts:
            for opp_type, count in type_counts.items():
                logger.info(f"   📊 {opp_type}: {count}")

        return all_opportunities

    def _create_mev_opportunity_from_route(self, provider_name, quote, input_mint, output_mint, amount):
        """Convert MultiAggregatorRouter route to MevOpportunity format"""
        try:
            import time

            from engines.mev.opportunity_scanner import MevOpportunity

            out_amount = int(quote.get("outAmount", "0"))
            if out_amount <= amount:
                return None  # Not profitable

            profit = out_amount - amount
            profit_percentage = (profit / amount) * 100 if amount > 0 else 0

            if profit_percentage < 0.5:  # Minimum 0.5% profit threshold
                return None

            return MevOpportunity(
                opportunity_type=f"cross_dex_{provider_name}",
                expected_profit=profit / 1e9,  # Convert to SOL
                # F006 FIX: Dynamic confidence calculation based on market data
                confidence_score=self._calculate_cross_dex_confidence(provider_name, best_route),
                execution_params={
                    "provider": provider_name,
                    "input_mint": input_mint,
                    "output_mint": output_mint,
                    "amount": amount,
                    "quote": quote,
                    "source": "multi_aggregator_router",
                },
                discovery_time=time.time(),
                execution_window_ms=3000,  # 3 second execution window
            )
        except Exception as e:
            logger.debug(f"Failed to create MEV opportunity from route: {e}")
            return None

    async def apply_next_generation_enhancements(self, opportunities: List) -> List:
        """Apply next-generation enhancements (extracted from monolith)"""
        logger.debug("🚀 Enhanced OpportunityScanner: Applying next-gen enhancements")
        return opportunities

    async def expand_token_universe(self) -> List[str]:
        """
        Expand token universe with dynamic discovery
        F016 FIX: Replace hardcoded token list with dynamic discovery
        """
        from config.centralized_config import config_manager
        from dex_providers.shared.token_discovery_service import TokenDiscoveryService

        try:
            token_discovery = TokenDiscoveryService(config_manager)
            expanded_tokens = await token_discovery.get_high_volume_tokens(
                min_volume_24h=config_manager.get("tokens.min_volume_24h", 100000),
                min_market_cap=config_manager.get("tokens.min_market_cap", 1000000),
                max_tokens=config_manager.get_max_expanded_tokens(),
                include_base_tokens=True,
            )
        except Exception as e:
            logger.warning(f"⚠️ Token discovery failed, using base tokens: {e}")
            # Fallback to base tokens
            expanded_tokens = [
                "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
                "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
                "So11111111111111111111111111111111111111112",  # SOL
            ]

        # Apply configurable limits
        max_tokens = config_manager.get_max_expanded_tokens()
        return expanded_tokens[:max_tokens]

    async def analyze_market_intelligence(self, opportunities: List) -> Dict[str, Any]:
        """Analyze market intelligence (extracted from monolith)"""
        return {
            "market_timing_score": self.calculate_market_timing_score(),
            "volatility_environment": self.assess_volatility_environment(),
            "liquidity_conditions": self.assess_liquidity_conditions(),
            "high_potential_signals": [],
        }

    def calculate_market_timing_score(self) -> float:
        """Calculate market timing score (extracted from monolith)"""
        current_hour = datetime.now().hour
        if 8 <= current_hour <= 18:  # Peak trading hours
            return 0.9
        elif 19 <= current_hour <= 23 or 6 <= current_hour <= 7:  # Extended hours
            return 0.7
        else:  # Off-peak hours
            return 0.5

    def assess_volatility_environment(self) -> str:
        """Assess volatility environment (extracted from monolith)"""
        return "moderate"

    def assess_liquidity_conditions(self) -> str:
        """Assess liquidity conditions (extracted from monolith)"""
        return "good"

    def calculate_intelligence_signal_strength(self, opportunity) -> float:
        """Calculate intelligence signal strength (extracted from monolith)"""
        signal_strength = 0.5  # Base signal strength

        # Boost for high profit opportunities
        if opportunity.expected_profit > 0.005:
            signal_strength += 0.2

        # Boost for high confidence
        if opportunity.confidence_score > 0.8:
            signal_strength += 0.2

        # Boost for intelligence-enhanced opportunities
        if opportunity.execution_params.get("intelligence_enhanced"):
            signal_strength += 0.1

        return min(1.0, signal_strength)

    async def apply_enhanced_opportunity_scoring(
        self, opportunities: List, market_intelligence: Dict[str, Any]
    ) -> List:
        """Apply enhanced opportunity scoring (extracted from monolith)"""
        for opportunity in opportunities:
            try:
                # Base scoring factors
                base_score = opportunity.confidence_score
                profit_factor = min(1.0, opportunity.expected_profit / 0.01)  # Normalize to 1% profit

                # Market intelligence factors
                timing_score = market_intelligence.get("market_timing_score", 0.6)

                # Calculate enhanced score
                enhanced_score = (
                    base_score * 0.50  # Base confidence
                    + profit_factor * 0.30  # Profit potential
                    + timing_score * 0.20  # Market timing
                )

                # Store enhanced metrics
                opportunity.execution_params["next_gen_enhanced"] = True
                opportunity.execution_params["next_gen_enhanced_score"] = min(1.0, enhanced_score)
                opportunity.execution_params["enhancement_factors"] = {
                    "base_score": base_score,
                    "profit_factor": profit_factor,
                    "timing_score": timing_score,
                }

            except Exception as e:
                logger.debug(f"Individual opportunity enhancement failed: {e}")
                continue

        return opportunities

    async def enhance_execution_probability(self, opportunities: List, market_intelligence: Dict[str, Any]) -> List:
        """Enhance execution probability (extracted from monolith)"""
        liquidity_environment = market_intelligence.get("liquidity_conditions", "moderate")
        volatility_environment = market_intelligence.get("volatility_environment", "moderate")

        # Calculate environment-based probability adjustments
        liquidity_boost = 0.1 if liquidity_environment == "good" else 0.0
        volatility_penalty = -0.05 if volatility_environment == "high" else 0.0

        for opportunity in opportunities:
            try:
                if opportunity.execution_params.get("next_gen_enhanced"):
                    current_score = opportunity.execution_params["next_gen_enhanced_score"]

                    # Apply environment adjustments
                    adjusted_score = current_score + liquidity_boost + volatility_penalty

                    # Update with execution probability enhancement
                    opportunity.execution_params["next_gen_enhanced_score"] = min(1.0, max(0.0, adjusted_score))
                    opportunity.execution_params["execution_probability_enhanced"] = True
                    opportunity.execution_params["environment_adjustments"] = {
                        "liquidity_boost": liquidity_boost,
                        "volatility_penalty": volatility_penalty,
                    }

            except Exception as e:
                logger.debug(f"Execution probability enhancement failed for opportunity: {e}")
                continue

        return opportunities

    def _calculate_cross_dex_confidence(self, provider_name: str, best_route: Any) -> float:
        """
        Calculate dynamic confidence for cross-DEX operations
        F006 FIX: Replace hardcoded confidence with market-data calculation
        """
        try:
            from config.centralized_config import config_manager
            from dex_providers.shared.confidence_calculator import ConfidenceCalculator

            confidence_calc = ConfidenceCalculator(config_manager)

            # Extract market data from route
            liquidity_score = getattr(best_route, "liquidity_score", 0.5)
            price_impact = getattr(best_route, "price_impact", 0.1)

            # Calculate dynamic confidence
            confidence = confidence_calc.calculate_cross_dex_confidence(
                provider_name=provider_name,
                market_liquidity=liquidity_score,
                price_impact=price_impact,
                execution_complexity=0.7,  # Cross-DEX complexity
            )

            return confidence

        except Exception as e:
            logger.warning(f"⚠️ Cross-DEX confidence calculation failed: {e}")
            # Fallback to config-based confidence
            from config.centralized_config import config_manager

            return config_manager.get("confidence.base_cross_dex_confidence", 0.7)
