#!/usr/bin/env python3
"""
📈 Advanced Technical Analysis Engine for MEV Trading Optimization

Sophisticated technical analysis indicators designed specifically for MEV trading:
- RSI, MACD, Bollinger Bands for momentum and trend analysis
- ATR volatility and volatility regime classification
- Support/resistance levels for slippage optimization
- Order flow imbalance and liquidity concentration analysis
- MEV-specific market microstructure indicators

Integration with ProfitMaximizationEngine for enhanced profit threshold optimization.
"""

import asyncio
import logging
import statistics
import time
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from config.enhanced_config_manager import EnhancedConfigManager
from dex_providers.shared.confidence_scoring_service import ComprehensiveConfidenceScorer
from dex_providers.shared.intelligent_arbitrage_cache_system import HotCache
from infrastructure.unified_network_congestion_service import (
    UnifiedNetworkCongestionService,
)
from engines.mev.config_manager import TechnicalAnalysisConfig

from .ml_slippage_prediction_engine import KALMAN_AVAILABLE, KalmanFilterPriceEstimator

logger = logging.getLogger(__name__)


class VolatilityRegime(Enum):
    """Volatility regime classifications for enhanced trading strategies"""

    LOW = "low"  # ATR < 25th percentile
    MEDIUM = "medium"  # ATR 25th-75th percentile
    HIGH = "high"  # ATR 75th-95th percentile
    EXTREME = "extreme"  # ATR > 95th percentile


class TrendDirection(Enum):
    """Trend direction classifications"""

    UPTREND = "uptrend"  # Strong bullish momentum
    DOWNTREND = "downtrend"  # Strong bearish momentum
    SIDEWAYS = "sideways"  # Choppy/consolidating


class MACDSignal(Enum):
    """MACD signal classifications"""

    BULLISH = "bullish"  # MACD line above signal line
    BEARISH = "bearish"  # MACD line below signal line
    NEUTRAL = "neutral"  # Lines crossed or flat


@dataclass
class AdvancedMarketAnalysis:
    """Enhanced market analysis with comprehensive technical indicators"""

    # Base market analysis fields (inherited concept) - no defaults first
    volatility_score: float
    trend_direction: float
    liquidity_depth: float
    mev_competition_level: float
    network_congestion: float
    optimal_trade_size: float
    confidence_score: float

    # Advanced technical indicators - no defaults
    rsi_score: float  # RSI (0-100) for overbought/oversold
    macd_signal: MACDSignal  # MACD signal classification
    momentum_strength: float  # 0.0-1.0 momentum intensity

    # Trend indicators - no defaults
    ema_trend: TrendDirection  # EMA-based trend direction
    trend_strength: float  # 0.0-1.0 trend confidence

    # Volatility indicators - no defaults
    bollinger_position: float  # Position relative to Bollinger Bands (-1 to 1)
    atr_volatility: float  # Average True Range volatility
    volatility_regime: VolatilityRegime  # Volatility classification

    # Market microstructure - no defaults
    order_flow_imbalance: float  # Buy/sell pressure (-1.0 to 1.0)
    liquidity_concentration: float  # Liquidity clustering score (0.0-1.0)

    # MEV-specific indicators - no defaults
    arbitrage_opportunity_score: float  # 0.0-1.0 arbitrage potential
    mev_competition_pressure: float  # 0.0-1.0 MEV bot competition
    flash_loan_viability: float  # 0.0-1.0 flash loan opportunity score

    # Fields with defaults last
    support_resistance_levels: List[float] = field(default_factory=list)
    spread_analysis: Dict[str, float] = field(default_factory=dict)
    analysis_timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    data_quality_score: float = 1.0  # 0.0-1.0 input data quality


class TechnicalAnalysisEngine:
    """
    📈 Advanced Technical Analysis Engine for MEV Trading Optimization

    FERTILIZE ENHANCED: Integrated with Kalman filter for oracle-free price estimation
    """

    def __init__(
        self,
        config: Optional[TechnicalAnalysisConfig] = None,
        network_service: Optional[UnifiedNetworkCongestionService] = None,
        enhanced_config_manager: Optional[EnhancedConfigManager] = None,
    ):
        self.config = config or TechnicalAnalysisConfig()

        # THEATER ELIMINATION: Enhanced configuration management
        self.enhanced_config_manager = enhanced_config_manager or EnhancedConfigManager()
        self.confidence_scorer = ComprehensiveConfidenceScorer(self.enhanced_config_manager)

        # Use configurable cache size (replaces hardcoded 100)
        cache_size = self.enhanced_config_manager.get_max_cache_size()
        self.market_data_cache = HotCache(max_size=cache_size, default_ttl=self.config.indicator_cache_ttl)

        # PHASE 3: Unified network service integration
        self.network_service = network_service

        # FERTILIZE ENHANCEMENT: Integrate Kalman filter price estimator
        if KALMAN_AVAILABLE:
            self.kalman_price_estimator = KalmanFilterPriceEstimator()
            logger.info("🌱 FERTILIZE: TechnicalAnalysisEngine enhanced with Kalman filter price estimation")
        else:
            self.kalman_price_estimator = None
            logger.warning("⚠️ FERTILIZE: Kalman filter unavailable for price estimation")

        # Indicator caching for performance
        self.indicator_cache: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, float] = {}

        # Historical data for trend analysis
        self.price_history: List[float] = []
        self.volume_history: List[float] = []
        self.timestamp_history: List[float] = []

        # Performance tracking
        self.calculation_times: List[float] = []
        self.cache_hit_rate: float = 0.0

        # PHASE 3: Performance tracking
        self.analysis_stats = {
            "total_analyses": 0,
            "cache_hits": 0,
            "calculation_time_samples": [],
            "network_service_calls": 0,
            "network_service_successes": 0,
        }

        logger.info(
            f"📈 Technical Analysis Engine initialized (Network: {bool(network_service)}, Kalman: {KALMAN_AVAILABLE})"
        )
        logger.info(
            f"   RSI period: {self.config.rsi_period}, MACD: "
            f"{self.config.macd_fast_period}/{self.config.macd_slow_period}"
        )
        logger.info(f"   Bollinger Bands: {self.config.bb_period}±{self.config.bb_std_dev}σ")

    async def analyze_market_technicals(
        self,
        price_data: List[float],
        volume_data: List[float],
        timestamp_data: List[int],
        current_price: Optional[float] = None,
    ) -> AdvancedMarketAnalysis:
        """
        Comprehensive technical analysis with FERTILIZE Kalman filter price estimation
        """
        start_time = time.time()
        self.analysis_stats["total_analyses"] += 1

        try:
            # Validate input data
            if len(price_data) < self.config.min_data_points:
                logger.warning(f"Insufficient price data: {len(price_data)} < {self.config.min_data_points}")
                return self._get_fallback_analysis(price_data, volume_data)

            # Ensure data consistency
            min_length = min(len(price_data), len(volume_data), len(timestamp_data))
            price_data = price_data[-min_length:]
            volume_data = volume_data[-min_length:]
            timestamp_data = timestamp_data[-min_length:]

            # Use current price if provided, otherwise use latest price
            current_price = current_price or price_data[-1]

            # FERTILIZE ENHANCEMENT: Kalman filter price estimation for oracle-free discovery
            kalman_price_estimate = current_price
            price_discovery_quality = 0.5  # Default quality
            oracle_independence_score = 0.0  # Default no independence

            if self.kalman_price_estimator and len(price_data) >= 10:
                try:
                    kalman_result = self.kalman_price_estimator.estimate_true_price(
                        recent_prices=price_data[-20:],  # Use recent 20 prices
                        current_price=current_price,
                    )

                    kalman_price_estimate = kalman_result["estimated_price"]
                    price_discovery_quality = kalman_result["estimation_quality"]
                    oracle_independence_score = kalman_result["price_efficiency"]

                    logger.debug(
                        f"🌱 FERTILIZE: Kalman estimated price: {kalman_price_estimate:.6f} "
                        f"(vs market: {current_price:.6f}, quality: {price_discovery_quality:.3f})"
                    )

                except Exception as e:
                    logger.warning(f"⚠️ FERTILIZE: Kalman price estimation failed: {e}")

            # Calculate momentum indicators
            rsi_score = await self._calculate_rsi(price_data)
            macd_signal, macd_line, macd_signal_line = await self._calculate_macd(price_data)
            momentum_strength = await self._calculate_momentum_strength(price_data, volume_data)

            # Calculate trend indicators
            ema_trend, trend_strength = await self._calculate_ema_trend_analysis(price_data)
            support_resistance_levels = await self._find_support_resistance_levels(price_data)

            # Calculate volatility indicators
            bollinger_position = await self._calculate_bollinger_position(price_data, current_price)
            atr_volatility = await self._calculate_atr(price_data)
            volatility_regime = await self._classify_volatility_regime(atr_volatility, price_data)

            # Enhanced market microstructure with Kalman insights
            order_flow_imbalance = await self._analyze_order_flow_imbalance_with_kalman(
                price_data, volume_data, kalman_price_estimate, current_price
            )
            liquidity_concentration = await self._calculate_liquidity_concentration_with_price_discovery(
                volume_data, price_discovery_quality
            )
            spread_analysis = await self._analyze_spread_dynamics_with_kalman(
                price_data, volume_data, kalman_price_estimate
            )

            # Calculate MEV-specific indicators
            arbitrage_score = await self._calculate_arbitrage_opportunity_score(
                price_data, volume_data, volatility_regime
            )
            mev_competition = await self._assess_mev_competition_pressure(price_data, volume_data, rsi_score)
            flash_loan_viability = await self._assess_flash_loan_viability(
                price_data, atr_volatility, liquidity_concentration
            )

            # Calculate base market analysis fields for integration
            base_volatility = min(1.0, atr_volatility * 10)  # Scale to 0-1
            base_trend = (
                trend_strength
                if ema_trend == TrendDirection.UPTREND
                else -trend_strength
                if ema_trend == TrendDirection.DOWNTREND
                else 0.0
            )

            # Data quality assessment
            data_quality = self._assess_data_quality(price_data, volume_data, timestamp_data)

            # PHASE 3: Get real-time network congestion for analysis
            network_congestion = await self._get_network_congestion_for_analysis()

            # Enhanced optimal trade size calculation with real network data
            optimal_trade_size = self._calculate_optimal_trade_size_with_network(
                liquidity_concentration, volatility_regime, network_congestion
            )

            calculation_time = time.time() - start_time
            self.calculation_times.append(calculation_time)

            # Enhanced analysis with Kalman insights
            analysis = AdvancedMarketAnalysis(
                # Base fields for ProfitMaximizationEngine integration
                volatility_score=base_volatility,
                trend_direction=base_trend,
                liquidity_depth=liquidity_concentration,
                mev_competition_level=mev_competition,
                network_congestion=network_congestion,  # ✅ REAL NETWORK DATA
                optimal_trade_size=optimal_trade_size,  # ✅ ENHANCED WITH NETWORK DATA
                confidence_score=data_quality,
                # Advanced technical indicators
                rsi_score=rsi_score,
                macd_signal=macd_signal,
                momentum_strength=momentum_strength,
                ema_trend=ema_trend,
                trend_strength=trend_strength,
                support_resistance_levels=support_resistance_levels,
                bollinger_position=bollinger_position,
                atr_volatility=atr_volatility,
                volatility_regime=volatility_regime,
                order_flow_imbalance=order_flow_imbalance,
                liquidity_concentration=liquidity_concentration,
                spread_analysis=spread_analysis,
                arbitrage_opportunity_score=arbitrage_score,
                mev_competition_pressure=mev_competition,
                flash_loan_viability=flash_loan_viability,
                data_quality_score=min(
                    1.0, data_quality + price_discovery_quality * 0.2
                ),  # Enhanced quality with Kalman
            )

            # Add Kalman insights to metadata
            if hasattr(analysis, "spread_analysis"):
                analysis.spread_analysis.update(
                    {
                        "kalman_price_estimate": kalman_price_estimate,
                        "price_discovery_quality": price_discovery_quality,
                        "oracle_independence_score": oracle_independence_score,
                        "price_deviation_bps": int(abs(kalman_price_estimate - current_price) / current_price * 10000)
                        if current_price > 0
                        else 0,
                    }
                )

            logger.debug(
                f"📈 Technical analysis complete: RSI={rsi_score:.1f}, "
                f"MACD={macd_signal.value}, network_congestion={network_congestion:.3f}"
            )
            logger.debug(f"   Calculation time: {calculation_time * 1000:.1f}ms")

            return analysis

        except Exception as e:
            logger.error(f"❌ Technical analysis failed: {e}")
            return self._get_fallback_analysis(price_data, volume_data)

    async def _calculate_rsi(self, price_data: List[float]) -> float:
        """Calculate Relative Strength Index (0-100)"""
        cache_key = f"rsi_{hash(tuple(price_data[-self.config.rsi_period :]))}"

        if self._is_cached(cache_key):
            return self.indicator_cache[cache_key]

        if len(price_data) < self.config.rsi_period + 1:
            return 50.0  # Neutral RSI

        # Calculate price changes
        price_changes = [price_data[i] - price_data[i - 1] for i in range(1, len(price_data))]

        # Separate gains and losses
        gains = [max(0, change) for change in price_changes]
        losses = [abs(min(0, change)) for change in price_changes]

        # Calculate average gains and losses
        if len(gains) >= self.config.rsi_period:
            avg_gain = sum(gains[-self.config.rsi_period :]) / self.config.rsi_period
            avg_loss = sum(losses[-self.config.rsi_period :]) / self.config.rsi_period
        else:
            avg_gain = sum(gains) / len(gains) if gains else 0.0
            avg_loss = sum(losses) / len(losses) if losses else 0.0

        # Calculate RSI
        if avg_loss == 0:
            rsi = 100.0
        else:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

        self._cache_indicator(cache_key, rsi)
        return rsi

    async def _calculate_macd(self, price_data: List[float]) -> Tuple[MACDSignal, float, float]:
        """Calculate MACD indicator and signal"""
        cache_key = (
            f"macd_{hash(tuple(price_data[-max(self.config.macd_slow_period, self.config.macd_signal_period) :]))}"
        )

        if self._is_cached(cache_key):
            cached = self.indicator_cache[cache_key]
            return cached["signal"], cached["macd_line"], cached["signal_line"]

        if len(price_data) < self.config.macd_slow_period:
            return MACDSignal.NEUTRAL, 0.0, 0.0

        # Calculate EMAs
        ema_fast = self._calculate_ema(price_data, self.config.macd_fast_period)
        ema_slow = self._calculate_ema(price_data, self.config.macd_slow_period)

        # MACD line
        macd_line = ema_fast - ema_slow

        # Signal line (EMA of MACD line)
        [ema_fast - ema_slow]  # Simplified for current calculation
        signal_line = macd_line  # Simplified - in production would use EMA of MACD history

        # Determine signal
        if macd_line > signal_line:
            signal = MACDSignal.BULLISH
        elif macd_line < signal_line:
            signal = MACDSignal.BEARISH
        else:
            signal = MACDSignal.NEUTRAL

        result = {"signal": signal, "macd_line": macd_line, "signal_line": signal_line}
        self._cache_indicator(cache_key, result)

        return signal, macd_line, signal_line

    async def _calculate_momentum_strength(self, price_data: List[float], volume_data: List[float]) -> float:
        """Calculate momentum strength (0.0-1.0)"""
        if len(price_data) < 10:
            return 0.5

        # Price momentum
        price_change_pct = (price_data[-1] - price_data[-10]) / price_data[-10]
        price_momentum = min(1.0, abs(price_change_pct) * 10)  # Scale to 0-1

        # Volume momentum
        recent_volume = sum(volume_data[-5:]) / 5 if len(volume_data) >= 5 else volume_data[-1]
        historic_volume = sum(volume_data[-20:-5]) / 15 if len(volume_data) >= 20 else recent_volume

        volume_ratio = recent_volume / historic_volume if historic_volume > 0 else 1.0
        volume_momentum = min(1.0, max(0.0, (volume_ratio - 0.5) * 2))  # Scale to 0-1

        # Combined momentum
        return price_momentum * 0.7 + volume_momentum * 0.3

    async def _calculate_ema_trend_analysis(self, price_data: List[float]) -> Tuple[TrendDirection, float]:
        """Calculate EMA-based trend analysis"""
        if len(price_data) < self.config.ema_long_period:
            return TrendDirection.SIDEWAYS, 0.0

        ema_short = self._calculate_ema(price_data, self.config.ema_short_period)
        ema_long = self._calculate_ema(price_data, self.config.ema_long_period)

        # Trend direction
        if ema_short > ema_long * 1.01:  # 1% threshold
            trend = TrendDirection.UPTREND
        elif ema_short < ema_long * 0.99:  # 1% threshold
            trend = TrendDirection.DOWNTREND
        else:
            trend = TrendDirection.SIDEWAYS

        # Trend strength (0.0-1.0)
        price_diff_pct = abs(ema_short - ema_long) / ema_long
        trend_strength = min(1.0, price_diff_pct * 50)  # Scale to 0-1

        return trend, trend_strength

    async def _find_support_resistance_levels(self, price_data: List[float]) -> List[float]:
        """Find key support and resistance levels"""
        if len(price_data) < self.config.sr_lookback_periods:
            return []

        levels = []
        window_size = min(10, len(price_data) // 5)  # Adaptive window size

        # Find local maxima and minima
        for i in range(window_size, len(price_data) - window_size):
            window = price_data[i - window_size : i + window_size + 1]
            center_price = price_data[i]

            # Check if local maximum (resistance)
            if center_price == max(window):
                levels.append(center_price)

            # Check if local minimum (support)
            elif center_price == min(window):
                levels.append(center_price)

        # Remove duplicate levels within tolerance
        unique_levels = []
        for level in sorted(levels):
            if not any(abs(level - existing) / existing < self.config.sr_touch_tolerance for existing in unique_levels):
                unique_levels.append(level)

        # Return most significant levels (limit to 10)
        return sorted(unique_levels)[-10:]

    async def _calculate_bollinger_position(self, price_data: List[float], current_price: float) -> float:
        """Calculate position relative to Bollinger Bands (-1 to 1)"""
        if len(price_data) < self.config.bb_period:
            return 0.0

        # Calculate Bollinger Bands
        recent_prices = price_data[-self.config.bb_period :]
        sma = sum(recent_prices) / len(recent_prices)
        std_dev = statistics.stdev(recent_prices)

        upper_band = sma + (self.config.bb_std_dev * std_dev)
        lower_band = sma - (self.config.bb_std_dev * std_dev)

        # Calculate position (-1 = at lower band, 0 = at middle, 1 = at upper band)
        if upper_band == lower_band:
            return 0.0

        position = (current_price - sma) / (upper_band - sma)
        return max(-1.0, min(1.0, position))

    async def _calculate_atr(self, price_data: List[float]) -> float:
        """Calculate Average True Range for volatility measurement"""
        if len(price_data) < self.config.atr_period + 1:
            return 0.01  # Default 1% volatility

        true_ranges = []
        for i in range(1, len(price_data)):
            high = price_data[i]
            low = price_data[i]
            close_prev = price_data[i - 1]

            tr1 = high - low
            tr2 = abs(high - close_prev)
            tr3 = abs(low - close_prev)

            true_ranges.append(max(tr1, tr2, tr3))

        # Calculate ATR
        if len(true_ranges) >= self.config.atr_period:
            atr = sum(true_ranges[-self.config.atr_period :]) / self.config.atr_period
        else:
            atr = sum(true_ranges) / len(true_ranges)

        return atr

    async def _classify_volatility_regime(self, atr: float, price_data: List[float]) -> VolatilityRegime:
        """Classify current volatility regime"""
        if len(price_data) < 50:
            return VolatilityRegime.MEDIUM

        # Calculate ATR percentiles from historical data
        historical_atrs = []
        for i in range(self.config.atr_period, len(price_data)):
            window = price_data[i - self.config.atr_period : i + 1]
            window_atr = await self._calculate_atr(window)
            historical_atrs.append(window_atr)

        if not historical_atrs:
            return VolatilityRegime.MEDIUM

        # Calculate percentiles
        sorted_atrs = sorted(historical_atrs)
        p25 = sorted_atrs[int(len(sorted_atrs) * 0.25)]
        p75 = sorted_atrs[int(len(sorted_atrs) * 0.75)]
        p95 = sorted_atrs[int(len(sorted_atrs) * 0.95)]

        # Classify current ATR
        if atr > p95:
            return VolatilityRegime.EXTREME
        elif atr > p75:
            return VolatilityRegime.HIGH
        elif atr > p25:
            return VolatilityRegime.MEDIUM
        else:
            return VolatilityRegime.LOW

    async def _analyze_order_flow_imbalance_with_kalman(
        self, price_data: List[float], volume_data: List[float], kalman_estimate: float, market_price: float
    ) -> float:
        """Enhanced order flow analysis using Kalman price estimation"""

        base_imbalance = await self._analyze_order_flow_imbalance(price_data, volume_data)

        # Kalman enhancement: detect price discovery premium/discount
        if market_price > 0:
            price_deviation = (kalman_estimate - market_price) / market_price

            # Adjust order flow based on price discovery
            # THEATER ELIMINATION: Replace hardcoded 0.005 with configurable deviation threshold
            deviation_threshold = self.enhanced_config_manager.get_deviation_threshold()
            if abs(price_deviation) > deviation_threshold:  # Configurable deviation threshold
                discovery_adjustment = price_deviation * 0.5  # Scale discovery impact
                enhanced_imbalance = base_imbalance + discovery_adjustment
                return max(-1.0, min(1.0, enhanced_imbalance))

        return base_imbalance

    async def _calculate_liquidity_concentration_with_price_discovery(
        self, volume_data: List[float], price_discovery_quality: float
    ) -> float:
        """Enhanced liquidity concentration calculation using price discovery quality"""

        base_concentration = await self._calculate_liquidity_concentration(volume_data)

        # Higher price discovery quality indicates better liquidity
        quality_boost = price_discovery_quality * 0.2  # Up to 20% boost
        enhanced_concentration = min(1.0, base_concentration + quality_boost)

        return enhanced_concentration

    async def _analyze_spread_dynamics_with_kalman(
        self, price_data: List[float], volume_data: List[float], kalman_estimate: float
    ) -> Dict[str, float]:
        """Enhanced spread analysis using Kalman price estimation"""

        base_spread_analysis = await self._analyze_spread_dynamics(price_data, volume_data)

        # Add Kalman-based spread insights
        if len(price_data) >= 2:
            recent_prices = price_data[-5:]  # Last 5 prices
            market_spread = (max(recent_prices) - min(recent_prices)) / min(recent_prices)

            # Calculate Kalman-based "true spread" estimation
            kalman_spread_estimate = market_spread * (1.0 - abs(kalman_estimate - price_data[-1]) / price_data[-1])

            base_spread_analysis.update(
                {
                    "kalman_spread_estimate": kalman_spread_estimate,
                    "spread_efficiency_ratio": kalman_spread_estimate / market_spread if market_spread > 0 else 1.0,
                    "price_discovery_spread_impact": abs(kalman_estimate - price_data[-1]) / price_data[-1]
                    if price_data[-1] > 0
                    else 0.0,
                }
            )

        return base_spread_analysis

    async def _calculate_arbitrage_opportunity_score(
        self, price_data: List[float], volume_data: List[float], volatility_regime: VolatilityRegime
    ) -> float:
        """Calculate arbitrage opportunity score (0.0-1.0)"""

        # Base score from volatility regime
        volatility_scores = {
            VolatilityRegime.LOW: 0.2,
            VolatilityRegime.MEDIUM: 0.5,
            VolatilityRegime.HIGH: 0.8,
            VolatilityRegime.EXTREME: 0.9,
        }
        base_score = volatility_scores[volatility_regime]

        # Adjust for volume (higher volume = better arbitrage conditions)
        if volume_data:
            recent_volume = sum(volume_data[-5:]) / 5 if len(volume_data) >= 5 else volume_data[-1]
            volume_multiplier = min(1.5, max(0.5, recent_volume / 1000))  # Scale factor
            base_score *= volume_multiplier

        return min(1.0, base_score)

    async def _assess_mev_competition_pressure(
        self, price_data: List[float], volume_data: List[float], rsi_score: float
    ) -> float:
        """Assess MEV competition pressure (0.0-1.0)"""

        # Higher competition during extreme RSI conditions
        if rsi_score > 80 or rsi_score < 20:
            competition_base = 0.8  # High competition during extreme conditions
        elif rsi_score > 70 or rsi_score < 30:
            competition_base = 0.6  # Medium competition
        else:
            competition_base = 0.4  # Lower competition during neutral RSI

        # Adjust for volume spikes (indicate MEV activity)
        if len(volume_data) >= 10:
            recent_avg = sum(volume_data[-5:]) / 5
            historic_avg = sum(volume_data[-20:-5]) / 15

            if historic_avg > 0:
                volume_spike = recent_avg / historic_avg
                # THEATER ELIMINATION: Replace hardcoded 2.0 with configurable threshold
                volume_spike_threshold = self.enhanced_config_manager.get_volume_spike_threshold()
                if volume_spike > volume_spike_threshold:  # Configurable volume spike threshold
                    competition_base = min(1.0, competition_base * 1.5)

        return min(1.0, competition_base)

    async def _assess_flash_loan_viability(
        self, price_data: List[float], atr_volatility: float, liquidity_concentration: float
    ) -> float:
        """Assess flash loan opportunity viability (0.0-1.0)"""

        # Higher ATR = better flash loan opportunities
        volatility_score = min(1.0, atr_volatility * 20)  # Scale factor

        # Higher liquidity concentration = better execution
        liquidity_score = liquidity_concentration

        # Price momentum factor
        if len(price_data) >= 5:
            momentum = abs(price_data[-1] - price_data[-5]) / price_data[-5]
            momentum_score = min(1.0, momentum * 50)  # Scale factor
        else:
            momentum_score = 0.5

        # Combined score
        combined_score = volatility_score * 0.4 + liquidity_score * 0.3 + momentum_score * 0.3
        return min(1.0, combined_score)

    def _calculate_ema(self, price_data: List[float], period: int) -> float:
        """Calculate Exponential Moving Average"""
        if len(price_data) < period:
            return sum(price_data) / len(price_data)

        multiplier = 2 / (period + 1)
        ema = price_data[0]

        for price in price_data[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def _assess_data_quality(
        self, price_data: List[float], volume_data: List[float], timestamp_data: List[int]
    ) -> float:
        """Assess quality of input data (0.0-1.0)"""
        quality_score = 1.0

        # Check data length
        if len(price_data) < self.config.min_data_points:
            quality_score *= 0.5

        # Check for missing or invalid data
        if any(p <= 0 for p in price_data):
            quality_score *= 0.7

        if any(v < 0 for v in volume_data):
            quality_score *= 0.8

        # Check timestamp consistency
        if len(timestamp_data) >= 2:
            time_gaps = [timestamp_data[i] - timestamp_data[i - 1] for i in range(1, len(timestamp_data))]
            if any(gap <= 0 for gap in time_gaps):
                quality_score *= 0.6

        return max(0.1, quality_score)  # Minimum 10% quality

    def _is_cached(self, cache_key: str) -> bool:
        """Check if indicator is cached and valid"""
        if cache_key not in self.indicator_cache:
            return False

        timestamp = self.cache_timestamps.get(cache_key, 0)
        return (time.time() - timestamp) < self.config.indicator_cache_ttl

    def _cache_indicator(self, cache_key: str, value: Any) -> None:
        """Cache indicator value with timestamp"""
        self.indicator_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

        # Cleanup old cache entries
        current_time = time.time()
        expired_keys = [
            key
            for key, timestamp in self.cache_timestamps.items()
            if current_time - timestamp > self.config.indicator_cache_ttl
        ]

        for key in expired_keys:
            self.indicator_cache.pop(key, None)
            self.cache_timestamps.pop(key, None)

    def _get_fallback_analysis(self, price_data: List[float], volume_data: List[float]) -> AdvancedMarketAnalysis:
        """Get fallback analysis for error cases with real network data when possible"""
        current_price = price_data[-1] if price_data else 1.0

        # PHASE 3: Try to get real network data even in fallback
        network_congestion = 0.5  # Default fallback
        if self.network_service:
            try:
                loop = asyncio.get_event_loop()
                if not loop.is_running():
                    congestion_data = loop.run_until_complete(
                        self.network_service.get_network_congestion("technical_analysis_fallback")
                    )
                    network_congestion = congestion_data.congestion_score
                    logger.debug(f"🌐 Fallback analysis using real network data: {network_congestion:.3f}")
            except Exception as e:
                logger.debug(f"⚠️ Could not get network data in fallback: {e}")

        # THEATER ELIMINATION: Replace hardcoded confidence with configurable value
        technical_analysis_confidence = self.enhanced_config_manager.get_technical_analysis_confidence()

        return AdvancedMarketAnalysis(
            # Base fields
            volatility_score=0.5,
            trend_direction=0.0,
            liquidity_depth=0.5,
            mev_competition_level=0.5,
            network_congestion=network_congestion,  # ✅ ENHANCED FALLBACK WITH REAL DATA
            optimal_trade_size=0.01,
            confidence_score=technical_analysis_confidence,  # THEATER ELIMINATED: Now configurable
            # Technical indicators (neutral values)
            rsi_score=50.0,
            macd_signal=MACDSignal.NEUTRAL,
            momentum_strength=0.5,
            ema_trend=TrendDirection.SIDEWAYS,
            trend_strength=0.0,
            support_resistance_levels=[current_price * 0.95, current_price * 1.05],
            bollinger_position=0.0,
            atr_volatility=0.01,
            volatility_regime=VolatilityRegime.MEDIUM,
            order_flow_imbalance=0.0,
            liquidity_concentration=0.5,
            spread_analysis={"avg_spread": 0.01, "spread_volatility": 0.005, "spread_trend": 0.0},
            arbitrage_opportunity_score=0.5,
            mev_competition_pressure=0.5,
            flash_loan_viability=0.5,
            data_quality_score=0.3,
        )

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get technical analysis performance statistics"""
        return {
            "avg_calculation_time_ms": (sum(self.calculation_times) / len(self.calculation_times) * 1000)
            if self.calculation_times
            else 0,
            "cache_entries": len(self.indicator_cache),
            "calculations_performed": len(self.calculation_times),
            "cache_hit_rate": self.cache_hit_rate,
            "config": {
                "rsi_period": self.config.rsi_period,
                "macd_periods": f"{self.config.macd_fast_period}/{self.config.macd_slow_period}",
                "bollinger_bands": f"{self.config.bb_period}±{self.config.bb_std_dev}σ",
                "atr_period": self.config.atr_period,
            },
        }

    async def _get_network_congestion_for_analysis(self) -> float:
        """
        PHASE 3: Get real-time network congestion for technical analysis

        Returns: Network congestion score (0.0-1.0) with real-time data
        """
        try:
            if self.network_service:
                self.analysis_stats["network_service_calls"] += 1
                congestion_data = await self.network_service.get_network_congestion("technical_analysis")
                self.analysis_stats["network_service_successes"] += 1

                logger.debug(
                    f"🌐 Network congestion for technical analysis: {congestion_data.congestion_score:.3f} "
                    f"(source: {congestion_data.source}, reliability: {congestion_data.reliability_score:.2f})"
                )

                return congestion_data.congestion_score
            else:
                # Fallback to static value when no service available
                logger.debug("⚠️ No network service - using static fallback for technical analysis")
                return 0.5  # Conservative fallback for technical analysis

        except Exception as e:
            logger.warning(f"⚠️ Network congestion check failed in technical analysis: {e}")
            return 0.5  # Conservative fallback for technical analysis

    def _calculate_optimal_trade_size_with_network(
        self, liquidity: float, volatility_regime: VolatilityRegime, network_congestion: float
    ) -> float:
        """
        PHASE 3: Enhanced optimal trade size calculation with real network data

        Args:
            liquidity: Liquidity concentration (0.0-1.0)
            volatility_regime: Current volatility regime
            network_congestion: Real-time network congestion (0.0-1.0)

        Returns:
            Optimal trade size adjusted for network conditions
        """
        # Base trade size from liquidity and volatility
        base_size = 0.01 * liquidity * (2.0 - volatility_regime.value * 0.1)

        # PHASE 3: Adjust for network congestion
        # Higher congestion -> smaller trades to reduce failed transactions
        network_adjustment = 1.0 - (network_congestion * 0.3)  # Up to 30% reduction

        # Volatility regime adjustments
        volatility_adjustments = {
            VolatilityRegime.LOW: 1.2,  # Larger trades in stable conditions
            VolatilityRegime.MEDIUM: 1.0,  # Standard sizing
            VolatilityRegime.HIGH: 0.8,  # Smaller trades in volatile conditions
            VolatilityRegime.EXTREME: 0.6,  # Much smaller trades in extreme volatility
        }

        volatility_adjustment = volatility_adjustments.get(volatility_regime, 1.0)

        # Calculate final optimal size
        optimal_size = base_size * network_adjustment * volatility_adjustment

        # Ensure reasonable bounds
        return max(0.005, min(optimal_size, 0.1))  # Between 0.5% and 10%


# Enhanced factory function with network service injection
def create_technical_analysis_engine(
    config: Optional[Dict[str, Any]] = None, network_service: Optional[UnifiedNetworkCongestionService] = None
) -> TechnicalAnalysisEngine:
    """
    Enhanced factory function for creating TechnicalAnalysisEngine
    - Initializes with optional config dictionary
    - Integrates with unified network congestion service
    - Provides context-aware network service if not explicitly provided
    """
    engine_config = TechnicalAnalysisConfig(**config) if config else TechnicalAnalysisConfig()

    return TechnicalAnalysisEngine(config=engine_config, network_service=network_service)
